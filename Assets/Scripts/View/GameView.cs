using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using UnityEngine.SceneManagement;
using DG.Tweening;
using Tidi.Ads;

public class GameView : BaseView
{
    public TextMeshProUGUI levelTxt;

    public TextMeshProUGUI remainUndoTxt;

    public TextMeshProUGUI coinTxt;

    private int undoRemain;

    public bool unlockHintView;

    public Transform coinIconInBoard;

    public GameObject hintRWIcon, undoRWIcon, undoDes,booster, boosterTut,boosterMask;

    public Button replayBtn, hintBtn;
   

    public void Replay()
    {
        AudioManager.instance.clickBtn.Play();
        GameManager.instance.ReplayGame();
        //SceneManager.LoadScene(0);
    }

    public void MoreBottle()
    {
        AudioManager.instance.clickBtn.Play();
        //GameManager.instance.levelGen.AddMoreBottle();
    }

    public void ShowBooster()
    {
        booster.SetActive(true);
    }

    public void ShowTutorial()
    {
        ShowBooster();
        boosterMask.SetActive(true);
        replayBtn.interactable = false;
        hintBtn.interactable = false;
        boosterTut.SetActive(true);
    }

    public void HideTutorial()
    {
        boosterMask.SetActive(false);
        replayBtn.interactable = true;
        hintBtn.interactable = true;
        boosterTut.SetActive(false);
    }

    public void HideBooster()
    {
        booster.SetActive(false);
    }

    public void UndoCB()
    {

        undoRemain = 6;
        PlayerPrefs.SetInt("Undo", undoRemain);

        remainUndoTxt.gameObject.SetActive(true);
        remainUndoTxt.text = undoRemain.ToString();
        undoDes.SetActive(false);
        undoRWIcon.SetActive(false);

    }

    public void Undo()
    {
        AudioManager.instance.clickBtn.Play();

       

        if (undoRemain > 0)
        {
            if (GameManager.instance.CanUndo())
            {
                undoRemain--;
                PlayerPrefs.SetInt("Undo", undoRemain);
                GameManager.instance.ProcessUndo();
                RefreshUndo();
            }
            
        }

        else
        {
            WatchVideoUndo();
        }

        if (GameManager.instance.currentLv == 3 && !GameManager.instance.finishFinalTut)
        {
            
                HideTutorial();
                GameManager.instance.finishFinalTut = true;
        }

    }

    public override void Start()
    {
       
    }

    public override void Update()
    {
        
    }

    private void RefreshUndo()
    {
        if (undoRemain > 0)
        {
            remainUndoTxt.gameObject.SetActive(true);
            remainUndoTxt.text = undoRemain.ToString();
            undoDes.SetActive(false);
            undoRWIcon.SetActive(false);
        }
        else
        {
            remainUndoTxt.gameObject.SetActive(false);
            undoDes.SetActive(true);
            undoRWIcon.SetActive(true);
        }
    }

    public override void InitView()
    {
        levelTxt.text = "Level " + GameManager.instance.currentLv.ToString();
        undoRemain = PlayerPrefs.GetInt("Undo");
        coinTxt.text = GameManager.instance.currentCoin.ToString();
        RefreshUndo();
    }

    public void ShowShop()
    {
        AudioManager.instance.clickBtn.Play();
        GameManager.instance.uiManager.shopView.ShowView();
    }

    public void ShowProfile()
    {
        AudioManager.instance.clickBtn.Play();
        GameManager.instance.uiManager.profileView.ShowView();

    }

    public void ShowLevelBonusView()
    {
        AudioManager.instance.clickBtn.Play();
        AudioManager.instance.cat.Play();
        GameManager.instance.uiManager.bonusLevelView.ShowView();
    }

    public void ShowHintViewCB()
    {
        
        GameManager.instance.uiManager.hintView.ShowView();
        
        if(!unlockHintView)
        {
            hintRWIcon.SetActive(false);
            unlockHintView = true;
        }
    }

    public void ShowHintView()
    {
        AudioManager.instance.clickBtn.Play();

        if (!unlockHintView)
            WatchVideoHint();
        else
            ShowHintViewCB();
    }


    public void WatchVideoHint()
    {

        if (AdManager.Instance.IsRewardedVideoLoaded())
        {
            AdManager.Instance.ShowRewardedVideo();
            AdManager.Instance.onWatchVideoReward += HandleOnWatchVideoRewardHint;
        }
        else
        {
            AdManager.Instance.RequestRewardedVideo();
        }
    }

    private void HandleOnWatchVideoRewardHint(bool succeed)
    {
        AdManager.Instance.onWatchVideoReward -= HandleOnWatchVideoRewardHint;

        if (succeed)
        {
            ShowHintViewCB();
        }
    }

    public void WatchVideoUndo()
    {

        if (AdManager.Instance.IsRewardedVideoLoaded())
        {
            AdManager.Instance.ShowRewardedVideo();
            AdManager.Instance.onWatchVideoReward += HandleOnWatchVideoRewardUndo;
        }
        else
        {
            AdManager.Instance.RequestRewardedVideo();
        }
    }

    private void HandleOnWatchVideoRewardUndo(bool succeed)
    {
        AdManager.Instance.onWatchVideoReward -= HandleOnWatchVideoRewardUndo;

        if (succeed)
        {
            UndoCB();
        }
    }
}
