using Tidi;
using Tidi.Ads;
using UnityEngine;

public class RatingView : BaseView
{
    [SerializeField] RatingView_StarButton[] _starButtons;

    int _currentSelectedStar;

    public override void InitView()
    {

    }

    public override void Start()
    {

    }

    public override void Update()
    {

    }

    public override void ShowView()
    {
        AudioManager.instance.waterFull.Play();
        base.ShowView();

        _currentSelectedStar = 5;
        for (int i = 0; i < _starButtons.Length; i++)
        {
            _starButtons[i].ConfigureData(i, HandleOnStarButtonPressed);
            _starButtons[i].Highlight(true);
        }
    }

    public override void HideView()
    {
        base.HideView();
        AudioManager.instance.clickBtn.Play();

        for (int i = 0; i < _starButtons.Length; i++)
            _starButtons[i].Reset();
    }

    public void RemindLaterPressed()
    {
        HideView();
    }

    public void Rate()
    {
        if (_currentSelectedStar == 5)
        {
            NativeScreen.CurrentScreen = NativeScreenTypes.RATING;
            Application.OpenURL("market://details?id=" + Application.identifier);
        }

        HideView();
    }

    private void HandleOnStarButtonPressed(int starIndex)
    {
        for (int i = 0; i < _starButtons.Length; i++)
        {
            _starButtons[i].Highlight(i <= starIndex);
        }

        _currentSelectedStar = starIndex + 1;

        AudioManager.instance.clickBtn.Play();
    }
}
