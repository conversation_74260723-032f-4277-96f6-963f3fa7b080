using UnityEngine;
using UnityEngine.UI;

public class RatingView_StarButton : MonoBehaviour
{
    [SerializeField] Image _highlightStarImage;

    System.Action<int> _callback;
    int _index;

    public void ConfigureData(int index, System.Action<int> callback)
    {
        _index = index;

        _callback = callback;
    }

    public void OnPressed()
    {
        _callback?.Invoke(_index);
    }

    public void Highlight(bool enabled)
    {
        _highlightStarImage.enabled = enabled;
    }

    public void Reset()
    {
        _callback = null;
    }
}