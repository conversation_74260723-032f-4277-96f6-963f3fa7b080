using Crystal;
using DG.Tweening;
using UnityEngine;

public class UIManager : MonoBehaviour
{
    public GameView gameView;

    public FinishView finishView;

    public ShopView shopView;

    public WarningView warningView;

    public WarningView bonusLevelView;

    public WarningView hintView;

    public ProfileView profileView;

    public RatingView ratingView;
    public ToastView toastView;

    public LoadingView loadingView;
    public CanvasGroup splashScreenCanvasGroup;

    SafeArea.SimDevice[] Sims;

    int SimIdx;

    // Start is called before the first frame update
    void Start()
    {

    }

    // Update is called once per frame
    void Update()
    {

    }

    public void InitView()
    {
        /*
        Sims = (SafeArea.SimDevice[])Enum.GetValues(typeof(SafeArea.SimDevice));
        ToggleSafeArea();
        */

        gameView.InitView();
        shopView.InitView();

        profileView.InitView();

        ratingView.InitView();
        
        loadingView.InitView();
    }

    /// <summary>
    /// Toggle the safe area simulation device.
    /// </summary>
    void ToggleSafeArea()
    {
        SimIdx++;

        if (SimIdx >= Sims.Length)
            SimIdx = 0;

        SafeArea.Sim = Sims[SimIdx];
        //Debug.LogFormat("Switched to sim device {0} with debug key '{1}'", Sims[SimIdx], KeySafeArea);
    }

    public void ShowSplashScreen()
    {
        splashScreenCanvasGroup.alpha = 1.0f;
        splashScreenCanvasGroup.interactable = true;
        splashScreenCanvasGroup.blocksRaycasts = true;
    }

    public void HideSplashScreen()
    {
        DOTween.To(() => splashScreenCanvasGroup.alpha, x => splashScreenCanvasGroup.alpha = x, 0.0f, 0.5f).SetEase(Ease.Linear)
           .OnComplete(() =>
           {
               splashScreenCanvasGroup.alpha = 0.0f;
               splashScreenCanvasGroup.interactable = false;
               splashScreenCanvasGroup.blocksRaycasts = false;
           });
    }

    public void UpdateLoadingProgress(float progressPercentage, float duration = 0.5f, System.Action onComplete = null)
    {
        loadingView.UpdateProgress(progressPercentage, duration, onComplete);
    }

    public void HideLoading()
    {
        loadingView.HideView();
    }

    public void ShowRatingPopup()
    {
        ratingView.ShowView();
    }

    public void HideRatingPopup()
    {
        ratingView.HideView();
    }

    public void ShowToast(string message)
    {
        toastView.ShowView(message);
    }
}
