using System.Collections;
using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.UI;

public class LoadingView : BaseView
{
    [SerializeField] Image _progressImage;

    public override void InitView()
    {
        _progressImage.fillAmount = 0;
    }

    public override void Start()
    {

    }

    public override void Update()
    {

    }

    public void UpdateProgress(float progressPercentage, float duration = 0.5f, System.Action onComplete = null)
    {
        DOTween.Kill("loading_animation_id");
        float fillAmount = (float)progressPercentage / 100f;
        _progressImage.DOFillAmount(fillAmount, duration).SetId("loading_animation_id").OnComplete(() =>
        {
            if (onComplete != null)
                onComplete();
        });
    }
}
