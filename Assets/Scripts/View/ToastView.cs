using System.Collections;
using UnityEngine;
using TMPro;

public class ToastView : BaseView
{
    [SerializeField] TextMeshProUGUI _messageText;

    public override void InitView()
    {
        
    }

    public override void Start()
    {
       
    }

    public override void Update()
    {
       
    }

    public override void ShowView()
    {
        base.ShowView();
    }

    public override void ShowView(string content)
    {
        base.ShowView(content);
        _messageText.text = content;

        StartCoroutine(YieldHideAfterTime());
    }

    private IEnumerator YieldHideAfterTime()
    {
        yield return new WaitForSeconds(3f);
        HideView();
    }

    public override void HideView()
    {
        base.HideView();
    }
}
