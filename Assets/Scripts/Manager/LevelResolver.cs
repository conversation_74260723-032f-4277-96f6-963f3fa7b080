using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class LevelResolver : MonoBehaviour
{
    private List<string> ConfigureGrid(string[] stacks, int numberOfStacks)
    {
        List<string> grid = new List<string>();
        for (int i = 0; i < numberOfStacks; i++)
            grid.Add(stacks[i]);
        return grid;
    }

    private int GetStackHeight(List<string> grid)
    {
        int max = 0;
        foreach (var stack in grid)
            if (max < stack.Length)
                max = stack.Length;
        return max;
    }

    private string CanonicalStringConversion(List<string> grid)
    {
        string finalString = "";
        grid.Sort();
        foreach (var stack in grid)
        {
            finalString += (stack + ";");
        }
        return finalString;
    }

    private bool IsSolved(List<string> grid, int stackHeight)
    {
        foreach (var stack in grid)
        {
            if (stack.Length == 0)
                continue;
            else if (stack.Length < stackHeight)
                return false;
            else if (stack.Count(c => c == stack[0]) != stackHeight)
                return false;
        }
        return true;
    }

    private bool IsValidMove(string sourceStack, string destinationStack, int height)
    {
        if (sourceStack.Length == 0 || destinationStack.Length == height)
            return false;
        int colorFreqs = sourceStack.Count(c => c == sourceStack[0]);
        if (colorFreqs == height)
            return false;
        if (destinationStack.Length == 0)
        {
            if (colorFreqs == sourceStack.Length)
                return false;
            return true;
        }
        return (sourceStack[sourceStack.Length - 1] == destinationStack[destinationStack.Length - 1]);
    }

    public bool SolvePuzzle(List<string> grid, int stackHeight, HashSet<string> visited, List<List<int>> answerMod)
    {
        if (stackHeight == -1)
        {
            stackHeight = GetStackHeight(grid);
        }
        visited.Add(CanonicalStringConversion(grid));
        for (int i = 0; i < grid.Count; i++)
        {
            string sourceStack = grid[i];
            for (int j = 0; j < grid.Count; j++)
            {
                if (i == j)
                    continue;
                string destinationStack = grid[j];
                if (IsValidMove(sourceStack, destinationStack, stackHeight))
                {
                    List<string> newGrid = new List<string>(grid);
                    newGrid[j] += newGrid[i].Last();
                    newGrid[i] = newGrid[i].Remove(newGrid[i].Length - 1);
                    if (IsSolved(newGrid, stackHeight))
                    {
                        answerMod.Add(new List<int> { i, j, 1 });
                        return true;
                    }
                    if (!visited.Contains(CanonicalStringConversion(newGrid)))
                    {
                        bool solveForTheRest = SolvePuzzle(newGrid, stackHeight, visited, answerMod);
                        if (solveForTheRest)
                        {
                            List<int> lastMove = answerMod[answerMod.Count - 1];
                            if (lastMove[0] == i && lastMove[1] == j)
                                answerMod[answerMod.Count - 1][2]++;
                            else
                                answerMod.Add(new List<int> { i, j, 1 });
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    public bool CheckGrid(List<string> grid)
    {
        int numberOfStacks = grid.Count;
        int stackHeight = GetStackHeight(grid);
        int numBallsExpected = ((numberOfStacks - 2) * stackHeight);
        int numBalls = 0;
        foreach (var stack in grid)
            numBalls += stack.Length;
        // if (numBalls != numBallsExpected)
        // {
        //     Logger.d("Grid has incorrect # of balls");
        //     return false;
        // }
        Dictionary<char, int> ballColorFrequency = new Dictionary<char, int>();
        foreach (var stack in grid)
            foreach (var ball in stack)
                if (ballColorFrequency.ContainsKey(ball))
                    ballColorFrequency[ball] += 1;
                else
                    ballColorFrequency[ball] = 1;
        foreach (var ballColor in ballColorFrequency)
        {
            if (ballColor.Value != GetStackHeight(grid))
            {
                Logger.d("Color " + ballColor.Key + " is not " + GetStackHeight(grid));
                return false;
            }
        }
        return true;
    }

    private void Start()
    {
        int numberOfStacks = 3;
        // string[] stacks = { "gbbb", "ybry", "yggy", "rrrg", "", "" };
        string[] stacks = { "gbbb", "gggb", "" };
        List<string> grid = ConfigureGrid(stacks, numberOfStacks);
        if (!CheckGrid(grid))
        {
            Logger.e("Invalid Grid");
            return;
        }
        if (IsSolved(grid, GetStackHeight(grid)))
        {
            Logger.d("Problem is already solved");
            return;
        }
        HashSet<string> visited = new HashSet<string>();
        List<List<int>> answerMod = new List<List<int>>();
        SolvePuzzle(grid, GetStackHeight(grid), visited, answerMod);
        answerMod.Reverse();
        foreach (var v in answerMod)
        {
            Logger.d("Move " + (v[0] + 1) + " to " + (v[1] + 1) + " " + v[2] + " times");
        }
    }
}


