using System.Collections;
using System.Collections.Generic;
#if USING_ADMOB
using GoogleMobileAds.Api;
using GoogleMobileAds.Common;
using GoogleMobileAds.Ump.Api;
#endif
using UnityEngine;

namespace Tidi.Ads
{
    public enum InterstitialTypes
    {
        GENERAL, RESUME, AD_BREAK
    }

    public enum WatchRewardedVideoState
    {
        NONE, SKIP, SUCCEED
    }

    public enum ShowInterstitialStates
    {
        None, CallShowing, ShowSuccess, ShowFailed, Clicked, Closed
    }

    public class AdManager : Singleton<AdManager>
    {
        #region Constants
        protected const string PREFS_WATCHED_VIDEO_TODAY = "prefs_watched_video_today";
        protected const string PREFS_LAST_WATCHED_VIDEO_DATE = "prefs_last_watched_video_date";
        protected const string PREFS_SHOWN_INTERSTITIAL_COUNT = "prefs_shown_interstitial_count";
        #endregion

        #region Events
        public delegate void OnWatchVideoReward(bool succeed);
        public OnWatchVideoReward onWatchVideoReward;

        public delegate void OnClosedInterstitial();
        public OnClosedInterstitial onClosedInterstitial;

        public delegate void OnAdsClicked();
        public OnAdsClicked onAdsClicked;
        #endregion

        #region Fields
        [Header("Settings")]
        [SerializeField]
        private AdSetting m_AdSetting;

        [SerializeField]
        private BaseAdUnitSetting[] m_AdUnitSettings;
        #endregion

        #region Properties
        private bool m_HasInitialized = false;
        public bool HasInitialized => m_HasInitialized;

        private Dictionary<AdProvider, IAdController> m_AdControllers;

        private bool m_ShowingRewardedVideo;
        private WatchRewardedVideoState m_WatchRewardedVideoState;

        private ShowInterstitialStates m_ShowInterstitialState = ShowInterstitialStates.None;

        private bool m_RequestingBanner;
        private Coroutine m_BannerCoroutine;
        public bool pAllowingBanner { get; set; } = true;
        public bool pCanShowBanner { get; set; } = true;

        private bool m_RequestingInterstitial;
        private Coroutine m_InterstitialCoroutine;

        private bool m_RequestingRewardedVideo;
        private Coroutine m_RewardedVideoCoroutine;

        private bool m_RequestingAppOpenAd;
        private Coroutine m_AppOpenAdCoroutine;

        double m_LastShownInterstitial = 0;
        int m_ShownInterstitialCountAllTime = 0;
        int m_ShownInterstitialCountOneSession = 0;

        #region Events Tracking
        string _eventInterstitialCallShowing = "af_inters_ad_eligible";
        string _eventInterstitialLoaded = "af_inters_api_called";
        string _eventInterstitialDisplayed = "af_inters_displayed";
        string _eventRewardedVideoCallShowing = "af_rewarded_ad_eligible";
        string _eventRewardedVideoLoaded = "af_rewarded_api_called";
        string _eventRewardedVideoDisplayed = "af_rewarded_ad_displayed";
        string _eventAppOpenAdCallShowing = "af_aoa_ad_eligible";
        string _eventAppOpenAdLoaded = "af_aoa_api_called";
        string _eventAppOpenAdDisplayed = "af_aoa_displayed";
        #endregion
        #endregion

        #region Unity Events
        private void Start()
        {
        }

        private void Update()
        {
            HandleShowingRewardedVideoStates();
            HandleShowingInterstitialStates();
            CheckAdsStatusFrequently();
            HandleShowingBanner();
        }

        private void HandleShowingRewardedVideoStates()
        {
            if (m_ShowingRewardedVideo)
            {
                if (m_WatchRewardedVideoState == WatchRewardedVideoState.NONE)
                    return;

                if (m_WatchRewardedVideoState == WatchRewardedVideoState.SUCCEED)
                {
                    if (onWatchVideoReward != null)
                        onWatchVideoReward(true);
                }
                else if (m_WatchRewardedVideoState == WatchRewardedVideoState.SKIP)
                {
                    if (onWatchVideoReward != null)
                        onWatchVideoReward(false);
                }

                m_ShowingRewardedVideo = false;
                m_WatchRewardedVideoState = WatchRewardedVideoState.NONE;

                RequestRewardedVideo();
            }
        }

        private void HandleShowingInterstitialStates()
        {
            switch (m_ShowInterstitialState)
            {
                case ShowInterstitialStates.None:
                    break;
                case ShowInterstitialStates.CallShowing:
                    break;
                case ShowInterstitialStates.ShowSuccess:
                    break;
                case ShowInterstitialStates.Clicked:
                    break;
                case ShowInterstitialStates.Closed:
                case ShowInterstitialStates.ShowFailed:
                    m_ShowInterstitialState = ShowInterstitialStates.None;
                    m_LastShownInterstitial = Time.timeSinceLevelLoadAsDouble;

                    RequestInterstitial();
                    break;
            }
        }

        double _checkedAdsStatusTime = 0f;
        private void CheckAdsStatusFrequently()
        {
            if (!m_HasInitialized)
                return;

            if (Application.internetReachability == NetworkReachability.NotReachable)
                return;

            if (Time.timeSinceLevelLoadAsDouble - _checkedAdsStatusTime >= 10f)
            {
                if (pAllowingBanner && !m_RequestingBanner && !IsBannerLoaded())
                    RequestBanner();

                if (!m_RequestingInterstitial && !IsInterstitialLoaded())
                    RequestInterstitial();

                if (!m_RequestingRewardedVideo && !IsRewardedVideoLoaded())
                    RequestRewardedVideo();

                _checkedAdsStatusTime = Time.timeSinceLevelLoadAsDouble;
            }
        }

        protected override void OnDestroy()
        {
            if (m_AdControllers != null)
            {
                foreach (var pair in m_AdControllers)
                {
                    pair.Value.Dispose();
                }
            }

            base.OnDestroy();
        }

        private void OnApplicationFocus(bool isFocus)
        {
            Logger.d("AdManager OnApplicationFocus ", isFocus);
            if (m_AdControllers == null)
                return;

            if (isFocus) // On Focus
            {
                if (Time.realtimeSinceStartup <= 1f) // Startup state
                    return;

                var screenType = NativeScreen.CurrentScreen;
                if (screenType == NativeScreenTypes.NORMAL)
                {
                    if (!IsInterstitialLoaded() && m_ShowInterstitialState == ShowInterstitialStates.None)
                        RequestInterstitial();

                    if (!IsRewardedVideoLoaded())
                        RequestRewardedVideo();
                }

                if (screenType == NativeScreenTypes.LEADERBOARD && screenType == NativeScreenTypes.IAP)
                {
                    // TODO: Hide Waiting screen
                }

                NativeScreen.CurrentScreen = NativeScreenTypes.NORMAL;
            }
        }

        private void OnApplicationPause(bool isPaused)
        {
            if (m_AdControllers == null)
                return;

            foreach (var pair in m_AdControllers)
            {
                pair.Value?.OnApplicationPause(isPaused);
            }
        }

        #endregion

        #region Methods
        public void Init()
        {
            StartCoroutine(YieldInit());
        }

        private IEnumerator YieldInit()
        {
            m_AdControllers = new Dictionary<AdProvider, IAdController>();

            m_ShownInterstitialCountAllTime = PrefsUtils.GetInt(PREFS_SHOWN_INTERSTITIAL_COUNT, 0);

#if USING_ADMOB
            // ConsentInformation.Reset();

            // Wait For ATT Determined
#if UNITY_IOS
            var status = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
            while (status == ATTrackingStatusBinding.AuthorizationTrackingStatus.NOT_DETERMINED)
            {
                status = ATTrackingStatusBinding.GetAuthorizationTrackingStatus();
                yield return null;
            }
#endif

            bool enableAdmob = true;
            Time.timeScale = 0f;

            // Checking consent information
            // Set tag for under age of consent.
            // Here false means users are not under age of consent.
            ConsentRequestParameters request = new ConsentRequestParameters
            {
                TagForUnderAgeOfConsent = false,
            };

            FormError checkConsentFormError = null;
            bool checkingConsentInfo = true;
            System.Action<FormError> checkConsentInfoCallback = (consentError) =>
            {
                checkingConsentInfo = false;
                checkConsentFormError = consentError;
            };

            // Check the current consent information status.
            ConsentInformation.Update(request, checkConsentInfoCallback);

            yield return new WaitUntil(() => !checkingConsentInfo);

            if (checkConsentFormError != null)
            {
                // Handle the error.
                Logger.e("Ad Checking Consent", checkConsentFormError.ErrorCode, " | ErrorMessage: ", checkConsentFormError.Message);
                // Continue with Admob initialization
            }
            else
            {
                // If the error is null, the consent information state was updated.
                // You are now ready to check if a form is available.
                FormError loadingConsentFormError = null;
                bool loadingConsentInfo = true;
                System.Action<FormError> loadingConsentInfoCallback = (consentError) =>
                {
                    loadingConsentInfo = false;
                    loadingConsentFormError = consentError;
                };

                ConsentForm.LoadAndShowConsentFormIfRequired(loadingConsentInfoCallback);

                yield return new WaitUntil(() => !loadingConsentInfo);

                if (loadingConsentFormError != null)
                {
                    // Handle the error.
                    Logger.e("Ad Loading Consent", loadingConsentFormError.ErrorCode, " | ErrorMessage: ", loadingConsentFormError.Message);
                    // Continue with Admob initialization
                }
                else
                {
                    enableAdmob = ConsentInformation.CanRequestAds();
                    Logger.d("Did user consent information?? ", ConsentInformation.CanRequestAds());
                }
            }

            Time.timeScale = 1f;

            if (enableAdmob)
            {
                Admob admob = new Admob();
                admob.Init(this, GetAdUnitSettingByProvider(AdProvider.Admob));
                m_AdControllers[AdProvider.Admob] = admob;
            }
#endif

#if USING_UNITY_ADS
            UnityAds unity = new UnityAds();
            unity.Init(this, GetAdUnitSettingByProvider(AdProvider.UnityAds));
            m_AdControllers[AdProvider.UnityAds] = unity;
#endif

#if !UNITY_EDITOR && USING_FAN
            FAN fan = new FAN();
            fan.Init(this, GetAdUnitSettingByProvider(AdProvider.FacebookAudienceNetwork));
            m_AdControllers[AdProvider.FacebookAudienceNetwork] = fan;
#endif

#if USING_IRONSOURCE
            IronSourceAds ironSource = new IronSourceAds();
            ironSource.Init(this, GetAdUnitSettingByProvider(AdProvider.IronSource));
            m_AdControllers[AdProvider.IronSource] = ironSource;
#endif

#if USING_APPLOVIN
            Applovin applovin = new Applovin();
            applovin.Init(this, GetAdUnitSettingByProvider(AdProvider.Applovin));
            m_AdControllers[AdProvider.Applovin] = applovin;
#endif

            float waitingInitializationTime = Time.realtimeSinceStartup;
            bool allInitialized = false;
            while (!allInitialized && Time.realtimeSinceStartup - waitingInitializationTime <= 5f)
            {
                var localChecking = true;
                foreach (var pair in m_AdControllers)
                {
                    if (!pair.Value.pIsInitialized)
                    {
                        localChecking = false;
                        break;
                    }
                }

                allInitialized = localChecking;
                yield return null;
            }

            AddCalendars();
            SetupRewardedVideoLimitProperties();

            m_RequestingBanner = false;
            m_RequestingInterstitial = false;
            m_RequestingRewardedVideo = false;

            m_HasInitialized = true;

            yield return YieldLazyRequestAds();
        }

        private IEnumerator YieldLazyRequestAds()
        {
            var didShowTutorial = pAllowingBanner = true; // TODO: Handle tutorial and allowing banner

            if (didShowTutorial)
                RequestAppOpenAd();

            RequestInterstitial();
            // yield return new WaitForSecondsRealtime(2f);
            // RequestBanner();
            yield return new WaitForSecondsRealtime(5f);
            RequestRewardedVideo();
        }

        private BaseAdUnitSetting GetAdUnitSettingByProvider(AdProvider provider)
        {
            for (int i = 0; i < m_AdUnitSettings.Length; i++)
            {
                if (m_AdUnitSettings[i].Provider == provider)
                    return m_AdUnitSettings[i];
            }

            return null;
        }

        #region Banner
        bool _showingBanner = false;
        private void HandleShowingBanner()
        {
            if (!m_HasInitialized)
                return;

            if (_showingBanner)
                return;

            if (IsBannerLoaded() && pCanShowBanner && !PrefsUtils.GetBool(Consts.PREFS_DID_REMOVE_ADS))
            {
                ShowBanner();
                _showingBanner = true;
            }
        }

        public void RequestBanner(float delayTime = 0f)
        {
            if (!pAllowingBanner)
                return;

            if (m_RequestingBanner)
                return;

            if (PrefsUtils.GetBool(Consts.PREFS_DID_REMOVE_ADS))
                return;

            if (m_BannerCoroutine != null)
                StopCoroutine(m_BannerCoroutine);

            m_BannerCoroutine = StartCoroutine(DoRequestBanner(delayTime));
        }

        private IEnumerator DoRequestBanner(float delayTime)
        {
            yield return new WaitForSeconds(delayTime);
            Logger.d("Start requesting banner!");
            m_RequestingBanner = true;
            bool adLoaded = false;
            for (int i = 0; i < m_AdSetting.BannerAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.BannerAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    m_AdControllers[provider].RequestBanner((_loaded) =>
                    {
                        m_RequestingBanner = false;
                        adLoaded = _loaded;
                    });

                    yield return new WaitUntil(() => !m_RequestingBanner);
                    m_RequestingBanner = false;

                    if (adLoaded)
                    {
                        // yield return null;
                        // HideBanner();
                        break;
                    }
                }
            }
        }

        public void ShowBanner()
        {
            for (int i = 0; i < m_AdSetting.BannerAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.BannerAdPriority[i];
                if (m_AdControllers.ContainsKey(provider) && m_AdControllers[provider].IsBannerLoaded())
                {
                    Logger.d(provider, " showing banner!");
                    m_AdControllers[provider].ShowBanner();
                    _showingBanner = true;
                    break;
                }
            }
        }

        public void HideBanner()
        {
            for (int i = 0; i < m_AdSetting.BannerAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.BannerAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    m_AdControllers[provider].HideBanner();
                    _showingBanner = false;
                }
            }
        }

        public bool IsBannerLoaded()
        {
            bool hasLoaded = false;
            for (int i = 0; i < m_AdSetting.BannerAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.BannerAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    hasLoaded = m_AdControllers[provider].IsBannerLoaded();
                }

                if (hasLoaded)
                    break;
            }

            return hasLoaded;
        }
        #endregion

        #region App Open Ad
        public void RequestAppOpenAd()
        {
            if (m_RequestingAppOpenAd)
                return;

            if (PrefsUtils.GetBool(Consts.PREFS_DID_REMOVE_ADS))
                return;

            if (!PrefsUtils.GetBool(Consts.PREFS_ENABLE_APP_OPEN_AD, true))
                return;

            if (m_AppOpenAdCoroutine != null)
                StopCoroutine(m_AppOpenAdCoroutine);

            m_AppOpenAdCoroutine = StartCoroutine(DoRequestAppOpenAd());
        }

        private IEnumerator DoRequestAppOpenAd()
        {
            m_RequestingAppOpenAd = true;
            for (int i = 0; i < m_AdSetting.AppOpenAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.AppOpenAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    bool adLoaded = false;
                    float requestTime = Time.realtimeSinceStartup;
                    m_AdControllers[provider].RequestAppOpenAd((_loaded) =>
                    {
                        adLoaded = _loaded;
                    });

                    while (!adLoaded && Time.realtimeSinceStartup - requestTime < 15f)
                        yield return null;

                    if (adLoaded)
                        break;
                }
            }

            m_RequestingAppOpenAd = false;
        }

        public void ShowAppOpenAd()
        {
            if (PrefsUtils.GetBool(Consts.PREFS_DID_REMOVE_ADS))
                return;

            if (!PrefsUtils.GetBool(Consts.PREFS_ENABLE_APP_OPEN_AD, true))
                return;

            for (int i = 0; i < m_AdSetting.AppOpenAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.AppOpenAdPriority[i];
                if (m_AdControllers.ContainsKey(provider) && m_AdControllers[provider].IsAppOpenAdLoaded())
                {
                    NativeScreen.CurrentScreen = NativeScreenTypes.SHOWING_ADS;

                    m_AdControllers[provider].ShowAppOpenAd();
                    break;
                }
            }
        }

        public bool IsAppOpenAdLoaded()
        {
            bool hasLoaded = false;
            for (int i = 0; i < m_AdSetting.AppOpenAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.AppOpenAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    hasLoaded = m_AdControllers[provider].IsAppOpenAdLoaded();
                }

                if (hasLoaded)
                    break;
            }

            return hasLoaded;
        }
        #endregion

        #region Interstitial
        public void RequestInterstitial()
        {
            if (PrefsUtils.GetBool(Consts.PREFS_DID_REMOVE_ADS))
                return;

            if (m_RequestingInterstitial)
                return;

            m_ShowInterstitialState = ShowInterstitialStates.None;
            m_InterstitialCoroutine = StartCoroutine(DoRequestInterstitial());
        }

        private IEnumerator DoRequestInterstitial()
        {
            m_RequestingInterstitial = true;
            for (int i = 0; i < m_AdSetting.InterstitialAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.InterstitialAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    bool adLoaded = false;
                    float requestTime = Time.realtimeSinceStartup;
                    m_AdControllers[provider].RequestInterstitial((_loaded) =>
                    {
                        adLoaded = _loaded;
                    });

                    while (!adLoaded && Time.realtimeSinceStartup - requestTime < 15f)
                        yield return null;

                    if (adLoaded)
                        break;
                }
            }

            m_RequestingInterstitial = false;
        }

        public void ShowInterstitial(InterstitialTypes interstitialType = InterstitialTypes.GENERAL)
        {
            if (PrefsUtils.GetBool(Consts.PREFS_DID_REMOVE_ADS))
                return;

            if (interstitialType != InterstitialTypes.RESUME)
            {
                var interstitialInterval = PrefsUtils.GetInt(Consts.PREFS_INTERSTITIAL_INTERVAL, 30);
                if (Time.timeSinceLevelLoadAsDouble - m_LastShownInterstitial < interstitialInterval && m_ShownInterstitialCountOneSession > 0)
                {
                    Logger.d($"Not enough time for showing next interstitial! Last shown: {m_LastShownInterstitial}, current time: {Time.timeSinceLevelLoadAsDouble}, interval: {interstitialInterval}");
                    return;
                }
            }

            for (int i = 0; i < m_AdSetting.InterstitialAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.InterstitialAdPriority[i];
                if (m_AdControllers.ContainsKey(provider) && m_AdControllers[provider].IsInterstitialLoaded())
                {
                    NativeScreen.CurrentScreen = NativeScreenTypes.SHOWING_ADS;
                    m_ShowInterstitialState = ShowInterstitialStates.CallShowing;

                    m_AdControllers[provider].ShowInterstitial();

                    m_LastShownInterstitial = Time.timeSinceLevelLoadAsDouble;
                    break;
                }
            }
        }

        public bool IsInterstitialLoaded()
        {
            bool hasLoaded = false;
            for (int i = 0; i < m_AdSetting.InterstitialAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.InterstitialAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    hasLoaded = m_AdControllers[provider].IsInterstitialLoaded();
                }

                if (hasLoaded)
                    break;
            }

            return hasLoaded;
        }
        #endregion

        #region Rewarded Video
        public void RequestRewardedVideo()
        {
            if (m_RequestingRewardedVideo)
                return;

            m_RewardedVideoCoroutine = StartCoroutine(DoRequestRewardedVideo());
        }

        private IEnumerator DoRequestRewardedVideo()
        {
            m_RequestingRewardedVideo = true;
            for (int i = 0; i < m_AdSetting.RewardedVideoAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.RewardedVideoAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    float requestTime = Time.realtimeSinceStartup;
                    bool adLoaded = false;
                    m_AdControllers[provider].RequestRewardedVideo((_loaded) =>
                    {
                        adLoaded = _loaded;
                    });

                    while (!adLoaded && Time.realtimeSinceStartup - requestTime < 15f)
                        yield return null;

                    if (adLoaded)
                        break;
                }
            }

            m_RequestingRewardedVideo = false;
        }

        public void ShowRewardedVideo()
        {
            for (int i = 0; i < m_AdSetting.RewardedVideoAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.RewardedVideoAdPriority[i];

                // if (DidReachRewardedVideoLimitPerDay(provider))
                //     continue;

                if (m_AdControllers.ContainsKey(provider) && m_AdControllers[provider].IsRewardedVideoLoaded())
                {
                    string prefsKey = string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(provider));
                    int watchedVideoToday = PlayerPrefs.GetInt(prefsKey, 0);
                    PlayerPrefs.SetInt(prefsKey, ++watchedVideoToday);

                    NativeScreen.CurrentScreen = NativeScreenTypes.SHOWING_ADS;

                    m_ShowingRewardedVideo = true;
                    m_AdControllers[provider].ShowRewardedVideo();
                    break;
                }
            }
        }

        public bool IsRewardedVideoLoaded()
        {
            bool hasLoaded = false;

            for (int i = 0; i < m_AdSetting.RewardedVideoAdPriority.Count; i++)
            {
                AdProvider provider = m_AdSetting.RewardedVideoAdPriority[i];
                if (m_AdControllers.ContainsKey(provider))
                {
                    hasLoaded = m_AdControllers[provider].IsRewardedVideoLoaded();
                }

                if (hasLoaded)
                    break;
            }

            return hasLoaded;
        }

        private void SetupRewardedVideoLimitProperties()
        {
            string lastWatchedVideoDateString = PlayerPrefs.GetString(PREFS_LAST_WATCHED_VIDEO_DATE, "");
            System.DateTime lastWatchedVideoDate;
            if (string.IsNullOrEmpty(lastWatchedVideoDateString))
            {
                lastWatchedVideoDate = System.DateTime.Now;
            }
            else
            {
                if (System.DateTime.TryParseExact(lastWatchedVideoDateString, "dd/MM/yyyy", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.None, out lastWatchedVideoDate))
                {
                    // Successful parse date
                }
                else
                {
                    lastWatchedVideoDate = System.DateTime.Now;
                }
            }

            bool isDiffDay = System.DateTime.Now.Subtract(lastWatchedVideoDate).TotalDays >= 1;

            if (isDiffDay || string.IsNullOrEmpty(lastWatchedVideoDateString))
            {
                PlayerPrefs.SetInt(string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(AdProvider.Admob)), 0);
                PlayerPrefs.SetInt(string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(AdProvider.UnityAds)), 0);
                PlayerPrefs.SetInt(string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(AdProvider.FacebookAudienceNetwork)), 0);
                PlayerPrefs.SetInt(string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(AdProvider.IronSource)), 0);
                PlayerPrefs.SetInt(string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(AdProvider.Applovin)), 0);
                PlayerPrefs.SetString(PREFS_LAST_WATCHED_VIDEO_DATE, System.DateTime.Now.ToString("dd/MM/yyyy"));
            }
        }

        private bool DidReachRewardedVideoLimitPerDay(AdProvider provider)
        {
            string prefsKey = string.Format("{0}_{1}", PREFS_WATCHED_VIDEO_TODAY, GetPrefsKeyByProvider(provider));
            int watchedVideoToday = PlayerPrefs.GetInt(prefsKey, 0);

            int rewardedVideoPerDay = m_AdSetting.RewardedVideoPerDay;

            return watchedVideoToday >= rewardedVideoPerDay;
        }

        private string GetPrefsKeyByProvider(AdProvider provider)
        {
            string prefsKey = "";
            if (provider == AdProvider.Admob)
                prefsKey = "admob";
            else if (provider == AdProvider.FacebookAudienceNetwork)
                prefsKey = "fan";
            else if (provider == AdProvider.UnityAds)
                prefsKey = "unity";
            else if (provider == AdProvider.IronSource)
                prefsKey = "iron";
            else if (provider == AdProvider.Applovin)
                prefsKey = "applovin";

            return prefsKey;
        }
        #endregion

        #region Callback
        public void HandleOnAdsClicked()
        {
            NativeScreen.CurrentScreen = NativeScreenTypes.CLICKED_ADS;
            onAdsClicked?.Invoke();
        }

        public void HandleOnClosedInterstitial()
        {
            m_ShowInterstitialState = ShowInterstitialStates.Closed;

            onClosedInterstitial?.Invoke();
        }

        public void HandleOnShowInterstitialFailed()
        {
            m_ShowInterstitialState = ShowInterstitialStates.ShowFailed;
        }

        public void HandleOnWatchVideoReward(bool succeed)
        {
            m_WatchRewardedVideoState = succeed ? WatchRewardedVideoState.SUCCEED : WatchRewardedVideoState.SKIP;
        }

        public void HandleInterstitialLoaded()
        {
        }

        public void HandleInterstitialDisplayed()
        {
            m_ShowInterstitialState = ShowInterstitialStates.ShowSuccess;

            m_ShownInterstitialCountAllTime++;
            m_ShownInterstitialCountOneSession++;

            PrefsUtils.SetInt(PREFS_SHOWN_INTERSTITIAL_COUNT, m_ShownInterstitialCountAllTime);
        }

        public void HandleRewardedVideoLoaded()
        {
        }

        public void HandleRewardedVideoDisplayed()
        {
        }

        public void HandleBannerLoaded(bool succeed)
        {
            // if (succeed)
            //     HideBanner();

            _showingBanner = false;
        }

        public void HandleAppOpenAdLoaded()
        {
        }

        public void HandleAppOpenAdDisplayed()
        {
        }

        public void HandleOnClosedAppOpenAd()
        {

        }
        #endregion

        public void AddCalendars()
        {
            new System.Globalization.ChineseLunisolarCalendar();
            new System.Globalization.HebrewCalendar();
            new System.Globalization.HijriCalendar();
            new System.Globalization.JapaneseCalendar();
            new System.Globalization.JapaneseLunisolarCalendar();
            new System.Globalization.KoreanCalendar();
            new System.Globalization.KoreanLunisolarCalendar();
            new System.Globalization.PersianCalendar();
            new System.Globalization.TaiwanCalendar();
            new System.Globalization.TaiwanLunisolarCalendar();
            new System.Globalization.ThaiBuddhistCalendar();
            new System.Globalization.UmAlQuraCalendar();
        }
        #endregion
    }
}
