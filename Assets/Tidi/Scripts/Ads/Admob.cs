#if USING_ADMOB
using System;
using System.Collections.Generic;
using AppsFlyerSDK;
using GoogleMobileAds.Api;
using UnityEngine;
using static GoogleMobileAds.Api.AdValue;

namespace Tidi.Ads
{
    public class Admob : IAdController
    {
        #region Constants
        #endregion

        #region Events
        #endregion

        #region Fields
        #endregion

        #region Properties
        private BannerView m_BannerView;

        private InterstitialAd m_Interstitial;
        private RewardedAd m_RewardedAd;

        private bool m_BannerLoaded = false;
        private bool m_HasRewarded = false;

        private System.Action<bool> m_OnBannerLoaded;
        private System.Action<bool> m_OnInterstitialLoaded;
        private System.Action<bool> m_OnVideoRewardedLoaded;

        private AdManager m_Manager;
        private BaseAdUnitSetting m_Settings;

        public bool pIsInitialized { get; set; } = false;
        #endregion

        #region Unity Events
        #endregion

        #region Methods
        public void Init(AdManager manager, BaseAdUnitSetting settings)
        {
            m_Manager = manager;
            m_Settings = settings;

            Logger.d(m_Settings.ProviderToString, "Is Initializing!");

            // Initialize the Google Mobile Ads SDK.
            MobileAds.Initialize((status) =>
            {
                pIsInitialized = true;
            });
        }

        public void OnApplicationPause(bool isPaused)
        {

        }

        public void Dispose()
        {
            m_RewardedAd = null;

            m_OnBannerLoaded = null;
            m_OnInterstitialLoaded = null;
            m_OnVideoRewardedLoaded = null;
        }

        #region Rewarded video
        public void RequestRewardedVideo(Action<bool> onLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting rewarded video!");
            m_OnVideoRewardedLoaded = onLoaded;

            if (IsRewardedVideoLoaded())
            {
                m_OnVideoRewardedLoaded?.Invoke(true);
                return;
            }

            if (m_RewardedAd != null)
                m_RewardedAd?.Destroy();

            m_RewardedAd = null;

            m_HasRewarded = false;

            // Create an empty ad request.
            AdRequest request = new AdRequest();
            // Load the rewarded ad with the request.
            RewardedAd.Load(m_Settings.RewardedVideoId, request, (RewardedAd ad, LoadAdError error) =>
            {
                // If the operation failed with a reason.
                if (error != null)
                {
                    HandleRewardedAdFailedToLoad(error.GetMessage());
                    return;
                }
                // If the operation failed for unknown reasons.
                // This is an unexpected error, please report this bug if it happens.
                if (ad == null)
                {
                    HandleRewardedAdFailedToLoad("Unexpected error: Rewarded load event fired with null ad and null error.");
                    return;
                }

                // The operation completed successfully.
                Debug.Log("Rewarded ad loaded with response : " + ad.GetResponseInfo());
                m_RewardedAd = ad;

                HandleRewardedAdLoaded();

                // Called when an ad is shown.
                m_RewardedAd.OnAdFullScreenContentOpened += HandleRewardedAdOpening;
                // Called when an ad request failed to show.
                m_RewardedAd.OnAdFullScreenContentFailed += HandleRewardedAdFailedToShow;
                // Called when the ad is closed.
                m_RewardedAd.OnAdFullScreenContentClosed += HandleRewardedAdClosed;
            });

        }

        public void HandleRewardedAdLoaded()
        {
            Logger.d(m_Settings.ProviderToString, "HandleRewardedAdLoaded event received");

            m_OnVideoRewardedLoaded?.Invoke(true);
        }

        public void HandleRewardedAdFailedToLoad(string errorMessage)
        {
            Logger.d(m_Settings.ProviderToString,
                "HandleRewardedAdFailedToLoad event received with message: "
                                 + errorMessage);

            m_OnVideoRewardedLoaded?.Invoke(false);
        }

        public void HandleRewardedAdOpening()
        {
            Logger.d(m_Settings.ProviderToString, "HandleRewardedAdOpening event received");
        }

        public void HandleRewardedAdFailedToShow(AdError args)
        {
            Logger.d(m_Settings.ProviderToString,
                "HandleRewardedAdFailedToShow event received with message: "
                                 + args.GetMessage());
        }

        public void HandleRewardedAdClosed()
        {
            Logger.d(m_Settings.ProviderToString, "HandleRewardedAdClosed event received");

#if UNITY_IOS
            m_Manager.HandleOnWatchVideoReward(m_HasRewarded);
#endif

            m_RewardedAd = null;
        }

        public void HandleUserEarnedReward(Reward args)
        {
            string type = args.Type;
            double amount = args.Amount;
            Logger.d(m_Settings.ProviderToString,
                "HandleRewardedAdRewarded event received for "
                            + amount.ToString() + " " + type);

            m_HasRewarded = true;

#if !UNITY_IOS
            m_Manager.HandleOnWatchVideoReward(m_HasRewarded);
#endif
        }

        public void ShowRewardedVideo()
        {
            Logger.d(m_Settings.ProviderToString, "Show Rewarded Video!");
            if (m_RewardedAd.CanShowAd())
                m_RewardedAd.Show(HandleUserEarnedReward);
        }

        public bool IsRewardedVideoLoaded()
        {
            return (m_RewardedAd != null && m_RewardedAd.CanShowAd());
        }
        #endregion

        #region Banner
        public void RequestBanner(Action<bool> onBannerLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting banner!");
            m_OnBannerLoaded = onBannerLoaded;

#if UNITY_EDITOR
            if (m_OnBannerLoaded != null)
                m_OnBannerLoaded(false);
#endif

            if (IsBannerLoaded())
            {
                if (m_OnBannerLoaded != null)
                    m_OnBannerLoaded(true);
                return;
            }

            m_BannerLoaded = false;

            if (m_BannerView != null)
                m_BannerView.Destroy();

            string bannerId = m_Settings.BannerId;
            // bannerId = "ca-app-pub-3940256099942544/**********"; // For testing ad
            AdSize adaptiveSize = AdSize.GetPortraitAnchoredAdaptiveBannerAdSizeWithWidth(AdSize.FullWidth);
            m_BannerView = new BannerView(bannerId, AdSize.Banner, AdPosition.Bottom);

            // Called when an ad request has successfully loaded.
            m_BannerView.OnBannerAdLoaded += HandleOnBannerAdLoaded;
            // Called when an ad request failed to load.
            m_BannerView.OnBannerAdLoadFailed += HandleOnBannerAdFailedToLoad;
            // Called when an ad is clicked.
            m_BannerView.OnAdFullScreenContentOpened += HandleOnBannerAdOpened;
            // Called when the user returned from the app after an ad click.
            m_BannerView.OnAdFullScreenContentClosed += HandleOnBannerAdClosed;
            // Called when the ad click caused the user to leave the application.
            m_BannerView.OnAdClicked += HandleOnBannerAdLeftApplication;

            // Create an empty ad request.
            AdRequest request = new AdRequest();

            // Load the banner with the request.
            m_BannerView.LoadAd(request);
        }

        public bool IsBannerLoaded()
        {
            return (m_BannerLoaded && m_BannerView != null);
        }

        public void HandleOnBannerAdLoaded()
        {
            Logger.d(m_Settings.ProviderToString, "Banner HandleAdLoaded event received");
            m_BannerLoaded = true;
            if (m_OnBannerLoaded != null)
                m_OnBannerLoaded(true);
        }

        public void HandleOnBannerAdFailedToLoad(LoadAdError args)
        {
            Logger.d(m_Settings.ProviderToString, "Banner HandleFailedToReceiveAd event received with message: " + args.GetMessage());
            m_BannerLoaded = false;
            if (m_OnBannerLoaded != null)
                m_OnBannerLoaded(false);
        }

        public void HandleOnBannerAdOpened()
        {
            Logger.d(m_Settings.ProviderToString, "HandleAdOpened event received");
        }

        public void HandleOnBannerAdClosed()
        {
            Logger.d(m_Settings.ProviderToString, "HandleAdClosed event received");
        }

        public void HandleOnBannerAdLeftApplication()
        {
            Logger.d(m_Settings.ProviderToString, "HandleAdLeftApplication event received");
            m_Manager.HandleOnAdsClicked();
        }

        public void ShowBanner()
        {
            m_BannerView.Show();
        }

        public void HideBanner()
        {
            if (m_BannerView != null)
                m_BannerView.Hide();
        }
        #endregion

        #region Interstitial Ads
        public void RequestInterstitial(Action<bool> onInterstitialLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting interstitial!");
            m_OnInterstitialLoaded = onInterstitialLoaded;

#if UNITY_EDITOR
            if (m_OnInterstitialLoaded != null)
                m_OnInterstitialLoaded(false);
#else
            if (IsInterstitialLoaded())
            {
                if (m_OnInterstitialLoaded != null)
                    m_OnInterstitialLoaded(true);
                return;
            }
#endif

            if (m_Interstitial != null)
            {
                m_Interstitial.Destroy();
                m_Interstitial = null;
            }

            // Initialize an InterstitialAd.
            // Create an empty ad request.
            // AdRequest request = new AdRequest.Builder().AddTestDevice("60C4C5A97D15C17CD4A3FB729EE88EBC").Build();
            AdRequest request = new AdRequest();

            // Send the request to load the ad.
            InterstitialAd.Load(m_Settings.InterstitialId, request, (InterstitialAd ad, LoadAdError error) =>
            {
                // If the operation failed with a reason.
                if (error != null)
                {
                    HandleOnInterstitialAdFailedToLoad(error.GetMessage());
                    return;
                }
                // If the operation failed for unknown reasons.
                // This is an unexpected error, please report this bug if it happens.
                if (ad == null)
                {
                    HandleOnInterstitialAdFailedToLoad("Unexpected error: Interstitial load event fired with null ad and null error.");
                    return;
                }

                // The operation completed successfully.
                Debug.Log("Interstitial ad loaded with response : " + ad.GetResponseInfo());
                m_Interstitial = ad;

                HandleOnInterstitialAdLoaded();

                // Called when an ad is shown.
                m_Interstitial.OnAdFullScreenContentOpened += HandleOnInterstitialAdOpened;
                // Called when the ad is closed.
                m_Interstitial.OnAdFullScreenContentClosed += HandleOnInterstitialAdClosed;
                // Called when the ad click caused the user to leave the application.
                m_Interstitial.OnAdClicked += HandleOnInterstitialAdLeftApplication;
            });
        }

        public bool IsInterstitialLoaded()
        {
            return (m_Interstitial != null && m_Interstitial.CanShowAd());
        }

        public void HandleOnInterstitialAdLoaded()
        {
            Logger.d(m_Settings.ProviderToString, "Interstitial HandleAdLoaded event received");
            if (m_OnInterstitialLoaded != null)
                m_OnInterstitialLoaded(true);
        }

        public void HandleOnInterstitialAdFailedToLoad(string errorMessage)
        {
            Logger.d(m_Settings.ProviderToString, "Interstitial HandleFailedToReceiveAd event received with message: " + errorMessage);
            if (m_OnInterstitialLoaded != null)
                m_OnInterstitialLoaded(false);
        }

        public void HandleOnInterstitialAdOpened()
        {
            Logger.d(m_Settings.ProviderToString, "Interstitial HandleAdOpened event received");
        }

        public void HandleOnInterstitialAdClosed()
        {
            Logger.d(m_Settings.ProviderToString, "Interstitial HandleAdClosed event received");

            m_Manager.HandleOnClosedInterstitial();
        }

        public void HandleOnInterstitialAdLeftApplication()
        {
            Logger.d(m_Settings.ProviderToString, "Interstitial HandleAdLeftApplication event received");

            m_Manager.HandleOnAdsClicked();
        }

        public void ShowInterstitial()
        {
            Logger.d(m_Settings.ProviderToString, "Show Interstitial!");
            m_Interstitial.Show();
        }
        #endregion
        #endregion

        #region Open Ads
        private Action<bool> m_OnAppOpenAdLoaded;
        private AppOpenAd m_AppOpenAd;

        public void RequestAppOpenAd(Action<bool> onAppOpenAdLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting AppOpenAd!");
            m_OnAppOpenAdLoaded = onAppOpenAdLoaded;

#if UNITY_EDITOR
            if (m_OnAppOpenAdLoaded != null)
                m_OnAppOpenAdLoaded(false);
#else
            if (IsAppOpenAdLoaded())
            {
                if (m_OnAppOpenAdLoaded != null)
                    m_OnAppOpenAdLoaded(true);
                return;
            }
#endif

            if (m_AppOpenAd != null)
            {
                m_AppOpenAd.Destroy();
                m_AppOpenAd = null;
            }

            // Initialize an AppOpenAd.
            // Create an empty ad request.
            // AdRequest request = new AdRequest.Builder().AddTestDevice("60C4C5A97D15C17CD4A3FB729EE88EBC").Build();
            AdRequest request = new AdRequest();
            string unitId = m_Settings.AppOpenAdId;
            // unitId = "ca-app-pub-3940256099942544/**********"; // For testing purpose

            // Send the request to load the ad.
            AppOpenAd.Load(unitId, request, (AppOpenAd ad, LoadAdError error) =>
            {
                // If the operation failed with a reason.
                if (error != null)
                {
                    HandleOnAppOpenAdAdFailedToLoad(error.GetMessage());
                    return;
                }
                // If the operation failed for unknown reasons.
                // This is an unexpected error, please report this bug if it happens.
                if (ad == null)
                {
                    HandleOnAppOpenAdAdFailedToLoad("Unexpected error: AppOpenAd load event fired with null ad and null error.");
                    return;
                }

                // The operation completed successfully.
                Debug.Log("AppOpenAd ad loaded with response : " + ad.GetResponseInfo());
                m_AppOpenAd = ad;

                HandleOnAppOpenAdAdLoaded();

                // Called when an ad is shown.
                m_AppOpenAd.OnAdFullScreenContentOpened += HandleOnAppOpenAdAdOpened;
                // Called when the ad is closed.
                m_AppOpenAd.OnAdFullScreenContentClosed += HandleOnAppOpenAdAdClosed;
                // Called when the ad click caused the user to leave the application.
                m_AppOpenAd.OnAdClicked += HandleOnAppOpenAdAdLeftApplication;
                // Raised when the ad is estimated to have earned money.
                m_AppOpenAd.OnAdPaid += HandleOnAppOpenAdPaid;
            });
        }

        public bool IsAppOpenAdLoaded()
        {
            return (m_AppOpenAd != null && m_AppOpenAd.CanShowAd());
        }

        private void HandleOnAppOpenAdPaid(AdValue adValue)
        {
            Logger.d(m_Settings.ProviderToString, String.Format("AppOpenAd HandleOnAppOpenAdPaid paid {0} {1}.",
                        adValue.Value,
                        adValue.CurrencyCode));

            double valueMicros = (double)adValue.Value / Math.Pow(10, 6);
            string currencyCode = adValue.CurrencyCode;
            PrecisionType precision = adValue.Precision;

            ResponseInfo responseInfo = m_AppOpenAd.GetResponseInfo();
            string responseId = responseInfo.GetResponseId();

            AdapterResponseInfo loadedAdapterResponseInfo = responseInfo.GetLoadedAdapterResponseInfo();
            string adSourceId = loadedAdapterResponseInfo.AdSourceId;
            string adSourceInstanceId = loadedAdapterResponseInfo.AdSourceInstanceId;
            string adSourceInstanceName = loadedAdapterResponseInfo.AdSourceInstanceName;
            string adSourceName = loadedAdapterResponseInfo.AdSourceName;
            string adapterClassName = loadedAdapterResponseInfo.AdapterClassName;
            long latencyMillis = loadedAdapterResponseInfo.LatencyMillis;
            Dictionary<string, string> credentials = loadedAdapterResponseInfo.AdUnitMapping;

            Firebase.Analytics.Parameter[] AdParameters = {
                    new Firebase.Analytics.Parameter("ad_platform", "ironSource"),
                    new Firebase.Analytics.Parameter("ad_source", adSourceName),
                    new Firebase.Analytics.Parameter("ad_unit_name", adSourceInstanceName),
                    new Firebase.Analytics.Parameter("ad_format", "aoa"),
                    new Firebase.Analytics.Parameter("currency",currencyCode),
                    new Firebase.Analytics.Parameter("value", valueMicros)
                };
            Analytics.AnalyticsHelper.LogEvent("ad_impression", AdParameters);

            Logger.d($"[IronSource] ImpressionDataReadyEvent, value {valueMicros}");

            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("ad_platform", "ironSource");
            dic.Add("ad_source", adSourceName);
            dic.Add("ad_unit_name", adSourceInstanceName);
            dic.Add("ad_format", "aoa");
            dic.Add("currency", currencyCode);
            dic.Add("value", valueMicros.ToString());
            AppsFlyerAdRevenue.logAdRevenue("admob", AppsFlyerAdRevenueMediationNetworkType.AppsFlyerAdRevenueMediationNetworkTypeGoogleAdMob, valueMicros, currencyCode, dic);
        }

        public void HandleOnAppOpenAdAdLoaded()
        {
            Logger.d(m_Settings.ProviderToString, "AppOpenAd HandleAdLoaded event received");
            if (m_OnAppOpenAdLoaded != null)
                m_OnAppOpenAdLoaded(true);

            m_Manager.HandleAppOpenAdLoaded();
        }

        public void HandleOnAppOpenAdAdFailedToLoad(string errorMessage)
        {
            Logger.d(m_Settings.ProviderToString, "AppOpenAd HandleFailedToReceiveAd event received with message: " + errorMessage);
            if (m_OnAppOpenAdLoaded != null)
                m_OnAppOpenAdLoaded(false);
        }

        public void HandleOnAppOpenAdAdOpened()
        {
            Logger.d(m_Settings.ProviderToString, "AppOpenAd HandleAdOpened event received");

            m_Manager.HandleAppOpenAdDisplayed();
        }

        public void HandleOnAppOpenAdAdClosed()
        {
            Logger.d(m_Settings.ProviderToString, "AppOpenAd HandleAdClosed event received");

            m_Manager.HandleOnClosedAppOpenAd();
        }

        public void HandleOnAppOpenAdAdLeftApplication()
        {
            Logger.d(m_Settings.ProviderToString, "AppOpenAd HandleAdLeftApplication event received");

            m_Manager.HandleOnAdsClicked();
        }

        public void ShowAppOpenAd()
        {
            Logger.d(m_Settings.ProviderToString, "Show AppOpenAd!");
            m_AppOpenAd.Show();
        }
        #endregion
    }
}
#endif
