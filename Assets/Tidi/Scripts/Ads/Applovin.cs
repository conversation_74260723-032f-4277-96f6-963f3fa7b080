#if USING_APPLOVIN
using System;
using System.Collections.Generic;
using AppsFlyerSDK;
using UnityEngine;
#if USING_AMAZON
using AmazonAds;
#endif

namespace Tidi.Ads
{
    public class Applovin : IAdController
    {
        private bool m_BannerLoaded = false;
        private bool m_HasRewarded = false;

        private System.Action<bool> m_OnBannerLoaded;
        private System.Action<bool> m_OnInterstitialLoaded;
        private System.Action<bool> m_OnVideoRewardedLoaded;

        private AdManager m_Manager;
        private BaseAdUnitSetting m_Settings;

        public bool pIsInitialized { get; set; } = false;

        private bool m_IsFirstLoadInterstitial = true;

        public void Dispose()
        {
            MaxSdkCallbacks.Interstitial.OnAdLoadedEvent -= OnInterstitialLoadedEvent;
            MaxSdkCallbacks.Interstitial.OnAdLoadFailedEvent -= OnInterstitialLoadFailedEvent;
            MaxSdkCallbacks.Interstitial.OnAdDisplayedEvent -= OnInterstitialDisplayedEvent;
            MaxSdkCallbacks.Interstitial.OnAdClickedEvent -= OnInterstitialClickedEvent;
            MaxSdkCallbacks.Interstitial.OnAdHiddenEvent -= OnInterstitialHiddenEvent;
            MaxSdkCallbacks.Interstitial.OnAdDisplayFailedEvent -= OnInterstitialAdFailedToDisplayEvent;

            MaxSdkCallbacks.Banner.OnAdLoadedEvent -= OnBannerAdLoadedEvent;
            MaxSdkCallbacks.Banner.OnAdLoadFailedEvent -= OnBannerAdLoadFailedEvent;
            MaxSdkCallbacks.Banner.OnAdClickedEvent -= OnBannerAdClickedEvent;
            MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent -= OnBannerAdRevenuePaidEvent;
            MaxSdkCallbacks.Banner.OnAdExpandedEvent -= OnBannerAdExpandedEvent;
            MaxSdkCallbacks.Banner.OnAdCollapsedEvent -= OnBannerAdCollapsedEvent;

            MaxSdkCallbacks.Rewarded.OnAdLoadedEvent -= OnRewardedAdLoadedEvent;
            MaxSdkCallbacks.Rewarded.OnAdLoadFailedEvent -= OnRewardedAdLoadFailedEvent;
            MaxSdkCallbacks.Rewarded.OnAdDisplayedEvent -= OnRewardedAdDisplayedEvent;
            MaxSdkCallbacks.Rewarded.OnAdClickedEvent -= OnRewardedAdClickedEvent;
            MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent -= OnRewardedAdRevenuePaidEvent;
            MaxSdkCallbacks.Rewarded.OnAdHiddenEvent -= OnRewardedAdHiddenEvent;
            MaxSdkCallbacks.Rewarded.OnAdDisplayFailedEvent -= OnRewardedAdFailedToDisplayEvent;
            MaxSdkCallbacks.Rewarded.OnAdReceivedRewardEvent -= OnRewardedAdReceivedRewardEvent;

            MaxSdkCallbacks.Interstitial.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
            MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
            MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
            MaxSdkCallbacks.MRec.OnAdRevenuePaidEvent -= OnAdRevenuePaidEvent;
        }

        public void OnApplicationPause(bool isPaused)
        {

        }

        public void Init(AdManager manager, BaseAdUnitSetting settings)
        {
            m_Manager = manager;
            m_Settings = settings;

            MaxSdkCallbacks.OnSdkInitializedEvent += (MaxSdkBase.SdkConfiguration sdkConfiguration) =>
            {
                pIsInitialized = true;
                // MaxSdk.ShowMediationDebugger();
                #if USING_AMAZON
                // Amazon.EnableLogging(true);
                // Amazon.EnableTesting(true);
                #endif

                MaxSdkCallbacks.Interstitial.OnAdLoadedEvent += OnInterstitialLoadedEvent;
                MaxSdkCallbacks.Interstitial.OnAdLoadFailedEvent += OnInterstitialLoadFailedEvent;
                MaxSdkCallbacks.Interstitial.OnAdDisplayedEvent += OnInterstitialDisplayedEvent;
                MaxSdkCallbacks.Interstitial.OnAdClickedEvent += OnInterstitialClickedEvent;
                MaxSdkCallbacks.Interstitial.OnAdHiddenEvent += OnInterstitialHiddenEvent;
                MaxSdkCallbacks.Interstitial.OnAdDisplayFailedEvent += OnInterstitialAdFailedToDisplayEvent;

                MaxSdkCallbacks.Banner.OnAdLoadedEvent += OnBannerAdLoadedEvent;
                MaxSdkCallbacks.Banner.OnAdLoadFailedEvent += OnBannerAdLoadFailedEvent;
                MaxSdkCallbacks.Banner.OnAdClickedEvent += OnBannerAdClickedEvent;
                MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent += OnBannerAdRevenuePaidEvent;
                MaxSdkCallbacks.Banner.OnAdExpandedEvent += OnBannerAdExpandedEvent;
                MaxSdkCallbacks.Banner.OnAdCollapsedEvent += OnBannerAdCollapsedEvent;

                MaxSdkCallbacks.Rewarded.OnAdLoadedEvent += OnRewardedAdLoadedEvent;
                MaxSdkCallbacks.Rewarded.OnAdLoadFailedEvent += OnRewardedAdLoadFailedEvent;
                MaxSdkCallbacks.Rewarded.OnAdDisplayedEvent += OnRewardedAdDisplayedEvent;
                MaxSdkCallbacks.Rewarded.OnAdClickedEvent += OnRewardedAdClickedEvent;
                MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent += OnRewardedAdRevenuePaidEvent;
                MaxSdkCallbacks.Rewarded.OnAdHiddenEvent += OnRewardedAdHiddenEvent;
                MaxSdkCallbacks.Rewarded.OnAdDisplayFailedEvent += OnRewardedAdFailedToDisplayEvent;
                MaxSdkCallbacks.Rewarded.OnAdReceivedRewardEvent += OnRewardedAdReceivedRewardEvent;

                MaxSdkCallbacks.Interstitial.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
                MaxSdkCallbacks.Rewarded.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
                MaxSdkCallbacks.Banner.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
                MaxSdkCallbacks.MRec.OnAdRevenuePaidEvent += OnAdRevenuePaidEvent;
            };

            MaxSdk.SetHasUserConsent(true);
            MaxSdk.SetIsAgeRestrictedUser(false);
            MaxSdk.SetDoNotSell(false);

            MaxSdk.SetSdkKey(m_Settings.GameId);
            // MaxSdk.SetUserId("USER_ID");
            MaxSdk.InitializeSdk();
        }

        private void OnAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo impressionData)
        {
            double revenue = impressionData.Revenue;
            var impressionParameters = new[]
            {
                new Firebase.Analytics.Parameter("ad_platform", "AppLovin"),
                new Firebase.Analytics.Parameter("ad_source", impressionData.NetworkName),
                new Firebase.Analytics.Parameter("ad_unit_name", impressionData.AdUnitIdentifier),
                new Firebase.Analytics.Parameter("ad_format", impressionData.AdFormat),
                new Firebase.Analytics.Parameter("value", revenue),
                new Firebase.Analytics.Parameter("currency", "USD"), // All AppLovin revenue is sent in USD
            };
            Firebase.Analytics.FirebaseAnalytics.LogEvent("ad_impression", impressionParameters);

            Logger.d($"[AppLovin] ImpressionDataReadyEvent, value {revenue}");

            Dictionary<string, string> dic = new Dictionary<string, string>();
            dic.Add("ad_platform", "AppLovin");
            dic.Add("ad_source", impressionData.NetworkName);
            dic.Add("ad_unit_name", impressionData.AdUnitIdentifier);
            dic.Add("ad_format", impressionData.AdFormat);
            dic.Add("currency", "USD");
            dic.Add("value", revenue.ToString());
            AppsFlyerAdRevenue.logAdRevenue("AppLovin", AppsFlyerAdRevenueMediationNetworkType.AppsFlyerAdRevenueMediationNetworkTypeApplovinMax, revenue, "USD", dic);
        }

        public void RequestInterstitial(Action<bool> onLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting interstitial!");
            m_OnInterstitialLoaded = onLoaded;

            if (IsInterstitialLoaded() && m_OnInterstitialLoaded != null)
            {
                m_OnInterstitialLoaded(true);
            }
            else
            {
                if (m_IsFirstLoadInterstitial)
                {
                    m_IsFirstLoadInterstitial = false;

                    #if USING_AMAZON
                    LoadInterstitialByAmazonPublisherService();
                    #endif
                }
                else
                {
                    MaxSdk.LoadInterstitial(m_Settings.InterstitialId);
                }
            }
        }

        #if USING_AMAZON
        private void LoadInterstitialByAmazonPublisherService()
        {
            var interstitialAd = new APSInterstitialAdRequest("a31de04e-0ae4-4d80-bfe5-91d076b53b22");
            interstitialAd.onSuccess += (adResponse) =>
            {
                MaxSdk.SetInterstitialLocalExtraParameter(m_Settings.InterstitialId, "amazon_ad_response", adResponse.GetResponse());
                MaxSdk.LoadInterstitial(m_Settings.InterstitialId);
            };
            interstitialAd.onFailedWithError += (adError) =>
            {
                MaxSdk.SetInterstitialLocalExtraParameter(m_Settings.InterstitialId, "amazon_ad_error", adError.GetAdError());
                MaxSdk.LoadInterstitial(m_Settings.InterstitialId);
            };

            interstitialAd.LoadAd();
        }
        #endif

        private void OnInterstitialLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad is ready for you to show. MaxSdk.IsInterstitialReady(adUnitId) now returns 'true'
            Logger.d(m_Settings.ProviderToString, "I got InterstitialAdReadyEvent");

            Logger.d(m_Settings.ProviderToString, " [Interstitial] ", $"Waterfall Name: {adInfo.WaterfallInfo.Name} and Test Name: {adInfo.WaterfallInfo.TestName}");
            Logger.d(m_Settings.ProviderToString, " [Interstitial] ", $"Waterfall latency was: {adInfo.WaterfallInfo.LatencyMillis} milliseconds");

            string waterfallInfoStr = "";
            foreach (var networkResponse in adInfo.WaterfallInfo.NetworkResponses)
            {
                waterfallInfoStr = "Network -> " + networkResponse.MediatedNetwork +
                                   "\n...adLoadState: " + networkResponse.AdLoadState +
                                   "\n...latency: " + networkResponse.LatencyMillis + " milliseconds" +
                                   "\n...credentials: " + networkResponse.Credentials;

                if (networkResponse.Error != null)
                {
                    waterfallInfoStr += "\n...error: " + networkResponse.Error;
                }
            }
            Logger.d(m_Settings.ProviderToString, " [Interstitial] ", waterfallInfoStr);

            m_OnInterstitialLoaded?.Invoke(true);

            m_Manager.HandleInterstitialLoaded();
        }

        private void OnInterstitialLoadFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Interstitial ad failed to load 
            // AppLovin recommends that you retry with exponentially higher delays, up to a maximum delay (in this case 64 seconds)
            Logger.e(m_Settings.ProviderToString, " [Interstitial] ", "I got InterstitialAdLoadFailedEvent, code: " + errorInfo.Code + ", description : " + errorInfo.Message);
            Logger.e(m_Settings.ProviderToString, " [Interstitial] ", "Waterfall Name: " + errorInfo.WaterfallInfo.Name + " and Test Name: " + errorInfo.WaterfallInfo.TestName);
            Logger.e(m_Settings.ProviderToString, " [Interstitial] ", "Waterfall latency was: " + errorInfo.WaterfallInfo.LatencyMillis + " milliseconds");

            foreach (var networkResponse in errorInfo.WaterfallInfo.NetworkResponses)
            {
                Logger.e(m_Settings.ProviderToString, " [Interstitial] ", "Network -> " + networkResponse.MediatedNetwork +
                      "\n...latency: " + networkResponse.LatencyMillis + " milliseconds" +
                      "\n...credentials: " + networkResponse.Credentials +
                      "\n...error: " + networkResponse.Error);
            }

            m_OnInterstitialLoaded?.Invoke(false);
        }

        private void OnInterstitialDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "I got InterstitialAdShowSucceededEvent");

            m_Manager.HandleInterstitialDisplayed();
        }

        private void OnInterstitialAdFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad failed to display. AppLovin recommends that you load the next ad.
            Logger.e(m_Settings.ProviderToString, "I got InterstitialAdShowFailedEvent, code :  " + errorInfo.Code + ", description : " + errorInfo.Message);
            m_Manager.HandleOnShowInterstitialFailed();
        }

        private void OnInterstitialClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "I got InterstitialAdClickedEvent");
            m_Manager.HandleOnAdsClicked();
        }

        private void OnInterstitialHiddenEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Interstitial ad is hidden. Pre-load the next ad.
            Logger.d(m_Settings.ProviderToString, "I got InterstitialAdClosedEvent");

            m_Manager.HandleOnClosedInterstitial();
        }

        public bool IsInterstitialLoaded()
        {
            return MaxSdk.IsInterstitialReady(m_Settings.InterstitialId);
        }

        public void ShowInterstitial()
        {
            Logger.d(m_Settings.ProviderToString, "Show Interstitial!");
            if (MaxSdk.IsInterstitialReady(m_Settings.InterstitialId))
            {
                MaxSdk.ShowInterstitial(m_Settings.InterstitialId);
            }
        }

        public bool IsBannerLoaded()
        {
            return m_BannerLoaded;
        }

        public void RequestBanner(Action<bool> onLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting banner!");
            m_OnBannerLoaded = onLoaded;

            if (IsBannerLoaded())
            {
                m_OnBannerLoaded?.Invoke(true);
            }
            else
            {
                m_BannerLoaded = false;

                #if USING_AMAZON
                CreateAdsByAmazonPublisherService();
                #else
                CreateMaxBannerAd();
                #endif
            }
        }

        #if USING_AMAZON
        private void CreateAdsByAmazonPublisherService()
        {
            int width;
            int height;
            string slotId;
            if (MaxSdkUtils.IsTablet())
            {
                width = 728;
                height = 90;
                slotId = "73e22d20-3401-45fa-9ab0-651d48983161";
            }
            else
            {
                width = 320;
                height = 50;
                slotId = "f372fb3d-a0a1-4bd4-bd74-6e8ba175f544";
            }

            var apsBanner = new APSBannerAdRequest(width, height, slotId);
            apsBanner.onSuccess += (adResponse) =>
            {
                MaxSdk.SetBannerLocalExtraParameter(m_Settings.BannerId, "amazon_ad_response", adResponse.GetResponse());
                CreateMaxBannerAd();
            };
            apsBanner.onFailedWithError += (adError) =>
            {
                MaxSdk.SetBannerLocalExtraParameter(m_Settings.BannerId, "amazon_ad_error", adError.GetAdError());
                CreateMaxBannerAd();
            };

            apsBanner.LoadAd();
        }
        #endif

        private void CreateMaxBannerAd()
        {
            // Banners are automatically sized to 320×50 on phones and 728×90 on tablets
            // You may call the utility method MaxSdkUtils.isTablet() to help with view sizing adjustments
            MaxSdk.CreateBanner(m_Settings.BannerId, MaxSdkBase.BannerPosition.BottomCenter);
            MaxSdk.SetBannerExtraParameter(m_Settings.BannerId, "adaptive_banner", "true");

            // Set background or background color for banners to be fully functional
            MaxSdk.SetBannerBackgroundColor(m_Settings.BannerId, new Color(1f, 1f, 1f, 0f));
        }

        public void ShowBanner()
        {
            MaxSdk.ShowBanner(m_Settings.BannerId);
        }

        public void HideBanner()
        {
            MaxSdk.HideBanner(m_Settings.BannerId);
        }

        private void OnBannerAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, " [Banner] ", $"Waterfall Name: {adInfo.WaterfallInfo.Name} and Test Name: {adInfo.WaterfallInfo.TestName}");
            Logger.d(m_Settings.ProviderToString, " [Banner] ", $"Waterfall latency was: {adInfo.WaterfallInfo.LatencyMillis} milliseconds");

            string waterfallInfoStr = "";
            foreach (var networkResponse in adInfo.WaterfallInfo.NetworkResponses)
            {
                waterfallInfoStr = "Network -> " + networkResponse.MediatedNetwork +
                                   "\n...adLoadState: " + networkResponse.AdLoadState +
                                   "\n...latency: " + networkResponse.LatencyMillis + " milliseconds" +
                                   "\n...credentials: " + networkResponse.Credentials;

                if (networkResponse.Error != null)
                {
                    waterfallInfoStr += "\n...error: " + networkResponse.Error;
                }
            }

            Logger.d(m_Settings.ProviderToString, " [Banner] ", waterfallInfoStr);
            Logger.d(m_Settings.ProviderToString, "Banner loaded!");
            m_OnBannerLoaded?.Invoke(true);
            m_BannerLoaded = true;

            m_Manager.HandleBannerLoaded(true);
        }

        private void OnBannerAdLoadFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            Logger.e(m_Settings.ProviderToString, "Banner failed to load with code: ", errorInfo.Code, ", description: ", errorInfo.Message);
            Logger.e(m_Settings.ProviderToString, " [Banner] ", "Waterfall Name: " + errorInfo.WaterfallInfo.Name + " and Test Name: " + errorInfo.WaterfallInfo.TestName);
            Logger.e(m_Settings.ProviderToString, " [Banner] ", "Waterfall latency was: " + errorInfo.WaterfallInfo.LatencyMillis + " milliseconds");

            foreach (var networkResponse in errorInfo.WaterfallInfo.NetworkResponses)
            {
                Logger.e(m_Settings.ProviderToString, " [Banner] ", "Network -> " + networkResponse.MediatedNetwork +
                      "\n...latency: " + networkResponse.LatencyMillis + " milliseconds" +
                      "\n...credentials: " + networkResponse.Credentials +
                      "\n...error: " + networkResponse.Error);
            }

            m_OnBannerLoaded?.Invoke(false);
            m_BannerLoaded = false;

            m_Manager.HandleBannerLoaded(false);
        }

        private void OnBannerAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "Banner clicked");
            m_Manager.HandleOnAdsClicked();
        }

        private void OnBannerAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "Banner AdRevenuePaidEvent");
        }

        private void OnBannerAdExpandedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "Banner AdExpandedEvent");
        }

        private void OnBannerAdCollapsedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "Banner AdCollapsedEvent");
        }

        public void RequestRewardedVideo(Action<bool> onLoaded)
        {
            Logger.d(m_Settings.ProviderToString, "requesting rewarded video!");
            m_OnVideoRewardedLoaded = onLoaded;

            m_HasRewarded = false;

            if (IsRewardedVideoLoaded() && m_OnVideoRewardedLoaded != null)
            {
                m_OnVideoRewardedLoaded(true);
            }
            else
            {
                MaxSdk.LoadRewardedAd(m_Settings.RewardedVideoId);
            }
        }

        public void ShowRewardedVideo()
        {
            Logger.d(m_Settings.ProviderToString, "Show Rewarded Video!");
            if (IsRewardedVideoLoaded())
            {
                MaxSdk.ShowRewardedAd(m_Settings.RewardedVideoId);

                m_HasRewarded = false;
            }
        }

        public bool IsRewardedVideoLoaded()
        {
            return MaxSdk.IsRewardedAdReady(m_Settings.RewardedVideoId);
        }

        private void OnRewardedAdLoadedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad is ready for you to show. MaxSdk.IsRewardedAdReady(adUnitId) now returns 'true'.

            Logger.d(m_Settings.ProviderToString, "I got OnRewardedAdLoadedEvent");
            m_OnVideoRewardedLoaded?.Invoke(true);

            m_Manager.HandleRewardedVideoLoaded();
        }

        private void OnRewardedAdLoadFailedEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo)
        {
            // Rewarded ad failed to load 
            // AppLovin recommends that you retry with exponentially higher delays, up to a maximum delay (in this case 64 seconds).

            Logger.e(m_Settings.ProviderToString, "I got OnRewardedAdLoadFailedEvent, code :  " + errorInfo.Code + ", description : " + errorInfo.Message);
            m_OnVideoRewardedLoaded?.Invoke(false);
            m_HasRewarded = false;
        }

        private void OnRewardedAdDisplayedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "I got OnRewardedAdDisplayedEvent");

            m_Manager.HandleRewardedVideoDisplayed();
        }

        private void OnRewardedAdFailedToDisplayEvent(string adUnitId, MaxSdkBase.ErrorInfo errorInfo, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad failed to display. AppLovin recommends that you load the next ad.
            Logger.d(m_Settings.ProviderToString, "I got OnRewardedAdFailedToDisplayEvent");
            m_HasRewarded = false;
        }

        private void OnRewardedAdClickedEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            Logger.d(m_Settings.ProviderToString, "I got OnRewardedAdClickedEvent, name = " + adInfo.DspName);
            m_Manager.HandleOnAdsClicked();
        }

        private void OnRewardedAdHiddenEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Rewarded ad is hidden. Pre-load the next ad
            Logger.d(m_Settings.ProviderToString, "I got OnRewardedAdHiddenEvent");
            m_Manager.HandleOnWatchVideoReward(m_HasRewarded);
        }

        private void OnRewardedAdReceivedRewardEvent(string adUnitId, MaxSdk.Reward reward, MaxSdkBase.AdInfo adInfo)
        {
            // The rewarded ad displayed and the user should receive the reward.
            Logger.d(m_Settings.ProviderToString, "I got OnRewardedAdReceivedRewardEvent, amount = " + reward.Amount + " name = " + reward.Label);
            m_HasRewarded = true;
        }

        private void OnRewardedAdRevenuePaidEvent(string adUnitId, MaxSdkBase.AdInfo adInfo)
        {
            // Ad revenue paid. Use this callback to track user revenue.
        }

        public void RequestAppOpenAd(Action<bool> onLoaded)
        {
            throw new NotImplementedException();
        }

        public bool IsAppOpenAdLoaded()
        {
            throw new NotImplementedException();
        }

        public void ShowAppOpenAd()
        {
            throw new NotImplementedException();
        }
    }
}
#endif
