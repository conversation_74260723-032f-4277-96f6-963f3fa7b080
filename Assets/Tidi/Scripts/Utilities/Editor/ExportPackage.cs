using UnityEngine;
using UnityEditor;
 
public class ExportPackage{
    [MenuItem ("Tools/Tidi/Export Package")]
    static void export()
    {
        AssetDatabase.ExportPackage (AssetDatabase.GetAllAssetPaths(),PlayerSettings.productName + ".unitypackage",ExportPackageOptions.Interactive | ExportPackageOptions.Recurse | ExportPackageOptions.IncludeDependencies | ExportPackageOptions.IncludeLibraryAssets);
    }
}
 