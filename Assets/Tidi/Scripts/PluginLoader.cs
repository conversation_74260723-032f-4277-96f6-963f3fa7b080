using System.Collections;
using Tidi;
using Tidi.Ads;
using UnityEngine;

public class PluginLoader : MonoBehaviour
{
    private bool m_FirebaseReadyToUse = false;

    private void Awake()
    {
        StartCoroutine(DoWaitForLogQueuedEvents());
        StartCoroutine(DoWaitFirebaseForFetchingRemote());

        DG.Tweening.DOTween.Init();
    }

    private void Start()
    {
#if UNITY_EDITOR
        InitFirebase();
#elif UNITY_ANDROID
            int checkPlayServiceCode;
            if (IsPlayServicesAvailable(out checkPlayServiceCode))
                InitFirebase();
#endif
    }

    public IEnumerator DoLoad()
    {
        AdManager.Instance.Init();

        GameManager.instance.uiManager.UpdateLoadingProgress(100f, 8f);

        bool enableAOA = PrefsUtils.GetBool(Consts.PREFS_ENABLE_APP_OPEN_AD, true);

        var initialStartTime = Time.timeSinceLevelLoad;
        yield return new WaitUntil(() => (
            RemoteConfigure.Instance.hasFetched
            && Tidi.Ads.AdManager.Instance.HasInitialized
            && (Tidi.Ads.AdManager.Instance.IsAppOpenAdLoaded() || !enableAOA)
            // && LeaderboardManager.Instance.IsAuthenticated()
            ) || Time.timeSinceLevelLoad - initialStartTime >= 8f);

        bool loading = true;
        GameManager.instance.uiManager.UpdateLoadingProgress(100, 1f, () =>
        {
            loading = false;
        });

        yield return new WaitUntil(() => !loading);

        int openedGameTime = PrefsUtils.GetInt(Consts.PREFS_OPENED_GAME_TIME, 0);
        bool enableAppOpenAdFirstOpen = PrefsUtils.GetBool(Consts.PREFS_ENABLE_APP_OPEN_AD_FIRST_OPEN);
        bool shouldBeShowAOA = openedGameTime > 1 || enableAppOpenAdFirstOpen;
        if (shouldBeShowAOA)
            AdManager.Instance.ShowAppOpenAd();

        AdManager.Instance.RequestBanner();

        yield return new WaitForSeconds(0.5f);

        GameManager.instance.uiManager.HideLoading();
    }

    #region Plugins
    public bool IsPlayServicesAvailable(out int resultCode)
    {
        resultCode = 0;
        try
        {
            const string GoogleApiAvailability_Classname =
                "com.google.android.gms.common.GoogleApiAvailability";
            AndroidJavaClass clazz =
                new AndroidJavaClass(GoogleApiAvailability_Classname);
            AndroidJavaObject obj =
                clazz.CallStatic<AndroidJavaObject>("getInstance");

            var androidJC = new AndroidJavaClass("com.unity3d.player.UnityPlayer");
            var activity = androidJC.GetStatic<AndroidJavaObject>("currentActivity");

            resultCode = obj.Call<int>("isGooglePlayServicesAvailable", activity);
            Logger.d("Is Play Service Available: ", resultCode);

            // result codes from https://developers.google.com/android/reference/com/google/android/gms/common/ConnectionResult
            // 0 == success
            // 1 == service_missing
            // 2 == update service required
            // 3 == service disabled
            // 18 == service updating
            // 9 == service invalid
            return resultCode == 0;
        }
        catch (System.Exception exc)
        {
            Logger.d("IsPlayServiceAvailable Exception: ", exc.Message);
            return false;
        }
    }

    private IEnumerator DoWaitForLogQueuedEvents()
    {
        while (!m_FirebaseReadyToUse || Time.realtimeSinceStartup < 10f)
            yield return null;
    }

    private System.Collections.IEnumerator DoWaitFirebaseForFetchingRemote()
    {
        Logger.d("Start waiting Firebase for fetching remote!");
        while (!m_FirebaseReadyToUse)
            yield return null;

        Logger.d("Firebase ready to use");

        RemoteConfigure.Instance.FetchData();
    }
    #endregion

}
